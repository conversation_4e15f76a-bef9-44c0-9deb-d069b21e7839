import UIKit
import SnapKit

/// 可复用的提现实名认证弹窗（支持微信、支付宝）
/// 用法：
/// let popup = WithdrawalRealnamePopupView(channel: .alipay)
/// popup.onConfirm = { input in /* 提交 input */ }
/// popup.present(in: self.view)
final class WithdrawalRealnamePopupView: UIView, UITextFieldDelegate {
    // MARK: - Types
    enum Channel {
        case wechat
        case alipay
        var title: String {
            switch self {
            case .wechat: return "微信提现实名"
            case .alipay: return "支付宝提现实名"
            }
        }
    }
    struct Input {
        let realName: String
        let idNumber: String
        let account: String? // 支付宝账号（支付宝模式下必填）
    }

    // MARK: - Public callbacks
    var onConfirm: ((Input) -> Void)?
    var onCancel: (() -> Void)?

    // MARK: - UI
    private let container = UIView()
    private let titleLabel = UILabel()
    private let stackView = UIStackView()

    private let nameField = UITextField()
    private let idField = UITextField()
    private let accountField = UITextField() // 仅在支付宝显示

    private let cancelButton = UIButton(type: .system)
    private let confirmButton = UIButton(type: .system)

    // MARK: - State
    private let channel: Channel

    // MARK: - Init
    init(channel: Channel, preset: Input? = nil) {
        self.channel = channel
        super.init(frame: .zero)
        setupUI()
        applyPreset(preset)
        updateConfirmState()
    }

    required init?(coder: NSCoder) { fatalError("init(coder:) has not been implemented") }

    // MARK: - Public
    func present(in view: UIView? = nil) {
        let target = view ?? UIApplication.shared.windows.first(where: { $0.isKeyWindow })
        guard let host = target else { return }
        frame = host.bounds
        autoresizingMask = [.flexibleWidth, .flexibleHeight]
        alpha = 0
        transform = CGAffineTransform(scaleX: 1.02, y: 1.02)
        host.addSubview(self)
        UIView.animate(withDuration: 0.22, delay: 0, options: [.curveEaseOut]) {
            self.alpha = 1
            self.transform = .identity
        }
    }

    func dismiss() {
        endEditing(true)
        UIView.animate(withDuration: 0.18, delay: 0, options: [.curveEaseIn]) {
            self.alpha = 0
            self.transform = CGAffineTransform(scaleX: 0.98, y: 0.98)
        } completion: { _ in
            self.removeFromSuperview()
        }
    }

    // MARK: - UI Setup
    private func setupUI() {
        backgroundColor = UIColor.black.withAlphaComponent(0.45)

        // tap to dismiss keyboard
        let tap = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        addGestureRecognizer(tap)

        // container
        addSubview(container)
        container.backgroundColor = .white
        container.layer.cornerRadius = 23
        container.layer.masksToBounds = true
        container.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.left.right.equalToSuperview().inset(32)
        }

        // title
        titleLabel.text = channel.title
        titleLabel.font = UIFont.boldSystemFont(ofSize: 19)
        titleLabel.textColor = UIColor(hex: "#3D3D3D")
        titleLabel.textAlignment = .center
        container.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.right.equalToSuperview().inset(16)
        }

        // stack of fields
        stackView.axis = .vertical
        stackView.spacing = 0
        container.addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(16)
            make.left.right.equalToSuperview()
        }

        // fields - 左标题右输入框，单行 Cell，高度 77
        configure(field: nameField, placeholder: "请输入姓名")
        configure(field: idField, placeholder: "请输入身份证号")
        idField.keyboardType = .asciiCapable
        idField.autocapitalizationType = .allCharacters
        let nameRow = rowView(title: "真实姓名", field: nameField)
        let idRow = rowView(title: "身份证号", field: idField)
        stackView.addArrangedSubview(nameRow)
        stackView.addArrangedSubview(idRow)

        if channel == .alipay {
            configure(field: accountField, placeholder: "请输入支付宝账号")
            accountField.keyboardType = .emailAddress
            let accountRow = rowView(title: "支付宝账号", field: accountField)
            stackView.addArrangedSubview(accountRow)
        }

        // buttons bar
        let btnBar = UIStackView()
        btnBar.axis = .horizontal
        btnBar.alignment = .center
        btnBar.spacing = 0
        btnBar.distribution = .fill
        container.addSubview(btnBar)
        btnBar.snp.makeConstraints { make in
            make.top.equalTo(stackView.snp.bottom).offset(25)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().inset(20)
        }

        // spacers: 确保左、中的、右三段间距相等
        let leftSpacer = UIView()
        let midSpacer = UIView()
        let rightSpacer = UIView()
        
        // cancel
        cancelButton.setTitle("取消", for: .normal)
        cancelButton.setTitleColor(UIColor(hex: "#666666"), for: .normal)
        cancelButton.backgroundColor = UIColor(hex: "#F2F2F2")
        cancelButton.layer.cornerRadius = 17
        cancelButton.titleLabel?.font = UIFont.systemFont(ofSize: 13)
        cancelButton.addTarget(self, action: #selector(cancelTapped), for: .touchUpInside)
        btnBar.addArrangedSubview(leftSpacer)
        btnBar.addArrangedSubview(cancelButton)
        btnBar.addArrangedSubview(midSpacer)

        // confirm
        confirmButton.setTitle("确认", for: .normal)
        confirmButton.setTitleColor(.white, for: .normal)
        confirmButton.backgroundColor = .appThemeOrange
        confirmButton.layer.cornerRadius = 17
        confirmButton.titleLabel?.font = UIFont.systemFont(ofSize: 13)
        confirmButton.addTarget(self, action: #selector(confirmTapped), for: .touchUpInside)
        btnBar.addArrangedSubview(confirmButton)
        btnBar.addArrangedSubview(rightSpacer)

        // 固定按钮尺寸
        cancelButton.snp.makeConstraints { make in
            make.width.equalTo(106)
            make.height.equalTo(34)
        }
        confirmButton.snp.makeConstraints { make in
            make.width.equalTo(106)
            make.height.equalTo(34)
        }

        // 等宽间隔，保证三段间距相等
        leftSpacer.snp.makeConstraints { make in
            make.width.equalTo(midSpacer)
        }
        rightSpacer.snp.makeConstraints { make in
            make.width.equalTo(midSpacer)
        }

        [nameField, idField, accountField].forEach { tf in
            tf.addTarget(self, action: #selector(textChanged), for: .editingChanged)
            tf.delegate = self
            tf.returnKeyType = .next
        }
        if channel == .wechat {
            idField.returnKeyType = .done
        } else {
            accountField.returnKeyType = .done
        }
    }

    private func configure(field: UITextField, placeholder: String) {
        field.borderStyle = .none
        field.clearButtonMode = .whileEditing
        field.font = UIFont.systemFont(ofSize: 15)
        field.textColor = UIColor(hex: "#333333")
        field.attributedPlaceholder = NSAttributedString(
            string: placeholder,
            attributes: [
                .foregroundColor: UIColor(hex: "#BFBFBF"),
                .font: UIFont.systemFont(ofSize: 15)
            ]
        )
    }

    private func rowView(title: String, field: UITextField) -> UIView {
        let row = UIView()
        row.snp.makeConstraints { make in
            make.height.equalTo(77)
        }

        // 标题
        let t = UILabel()
        t.text = title
        t.textColor = UIColor(hex: "#333333")
        t.font = UIFont.systemFont(ofSize: 15)
        row.addSubview(t)
        t.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(10)
            make.centerY.equalToSuperview()
        }

        // 输入背景
        let bg = UIView()
        bg.backgroundColor = UIColor(hex: "#F5F5F7")
        bg.layer.cornerRadius = 8
        row.addSubview(bg)
        bg.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(92)
            make.right.equalToSuperview().inset(14)
            make.height.equalTo(40)
            make.centerY.equalToSuperview()
        }

        // 输入框
        bg.addSubview(field)
        field.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.right.equalToSuperview().inset(12)
            make.centerY.equalToSuperview()
        }

        // 底部分割线
        let sep = UIView()
        sep.backgroundColor = UIColor(hex: "#EDEDED")
        row.addSubview(sep)
        sep.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(1)
        }

        return row
    }

    private func applyPreset(_ preset: Input?) {
        guard let preset = preset else { return }
        nameField.text = preset.realName
        idField.text = preset.idNumber
        accountField.text = preset.account
    }

    // MARK: - Actions
    @objc private func backgroundTapped() {
        endEditing(true)
    }

    @objc private func cancelTapped() {
        onCancel?()
        dismiss()
    }

    @objc private func confirmTapped() {
        guard let input = collectInput() else { return }
        onConfirm?(input)
        dismiss()
    }

    @objc private func textChanged() {
        updateConfirmState()
    }

    // MARK: - Helpers
    private func collectInput() -> Input? {
        let name = nameField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        let idNo = idField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        if channel == .wechat {
            guard !name.isEmpty, !idNo.isEmpty else { return nil }
            return Input(realName: name, idNumber: idNo, account: nil)
        } else {
            let account = accountField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
            guard !name.isEmpty, !idNo.isEmpty, !account.isEmpty else { return nil }
            return Input(realName: name, idNumber: idNo, account: account)
        }
    }

    private func updateConfirmState() {
        let isValid: Bool
        if channel == .wechat {
            isValid = !(nameField.text ?? "").isEmpty && !(idField.text ?? "").isEmpty
        } else {
            isValid = !(nameField.text ?? "").isEmpty && !(idField.text ?? "").isEmpty && !(accountField.text ?? "").isEmpty
        }
        confirmButton.alpha = isValid ? 1.0 : 0.6
        confirmButton.isEnabled = isValid
    }

    // MARK: - UITextFieldDelegate
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        if textField == nameField {
            idField.becomeFirstResponder()
        } else if textField == idField {
            if channel == .alipay {
                accountField.becomeFirstResponder()
            } else {
                textField.resignFirstResponder()
            }
        } else if textField == accountField {
            textField.resignFirstResponder()
        }
        return true
    }
}
