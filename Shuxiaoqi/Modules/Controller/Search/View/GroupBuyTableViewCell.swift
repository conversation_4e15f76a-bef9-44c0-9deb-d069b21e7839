import UIKit
import SnapKit
import Kingfisher

class GroupBuyTableViewCell: UITableViewCell {
    static let reuseIdentifier = "GroupBuyTableViewCell"

    // MARK: - UI
    /// 外层卡片容器，白色背景、圆角16并带阴影
    private let cardView: UIView = {
        let v = UIView()
        v.backgroundColor = .white
        v.layer.cornerRadius = 16
        v.layer.shadowColor = UIColor.black.withAlphaComponent(0.05).cgColor
        v.layer.shadowOpacity = 1
        v.layer.shadowRadius = 4
        v.layer.shadowOffset = CGSize(width: 0, height: 2)
        return v
    }()

    /// 店铺缩略图 32x32
    private let shopIcon: UIImageView = {
        let iv = UIImageView()
        iv.layer.cornerRadius = 4
        iv.clipsToBounds = true
        iv.backgroundColor = UIColor(hex: "#EEEEEE")
        iv.contentMode = .scaleAspectFill
        return iv
    }()

    /// 店铺名称
    private let shopNameLabel: UILabel = {
        let lb = UILabel()
        lb.font = .systemFont(ofSize: 15, weight: .medium)
        lb.textColor = UIColor(hex: "#333333")
        return lb
    }()

    /// 店铺分类
    private let categoryLabel: UILabel = {
        let lb = UILabel()
        lb.font = .systemFont(ofSize: 12)
        lb.textColor = UIColor(hex: "#777777")
        return lb
    }()

    /// 评分数字 Label（应显示如 4.3分）
    private let ratingLabel: UILabel = {
        let lb = UILabel()
        lb.font = .systemFont(ofSize: 12, weight: .bold)
        lb.textColor = UIColor(hex: "#FF8E15")
        return lb
    }()

    /// 星星容器，根据评分整数动态添加 icon_star_fill
    private let starStack = UIStackView()

    /// 人均 ¥xx
    private let avgPriceLabel: UILabel = {
        let lb = UILabel()
        lb.font = .systemFont(ofSize: 12, weight: .bold)
        lb.textColor = UIColor(hex: "#FF8E15")
        return lb
    }()

    /// 距离 Label
    private let distanceLabel: UILabel = {
        let lb = UILabel()
        lb.font = .systemFont(ofSize: 12)
        lb.textColor = UIColor(hex: "#777777")
        lb.textAlignment = .right
        return lb
    }()

    /// 顶部信息与商品信息分割线
    private let separator = UIView()

    /// 商品封面图 96x68
    private let productImageView: UIImageView = {
        let iv = UIImageView()
        iv.contentMode = .scaleAspectFill
        iv.clipsToBounds = true
        iv.layer.cornerRadius = 6
        iv.backgroundColor = UIColor(hex: "#EEEEEE")
        return iv
    }()

    /// 商品标题
    private let productNameLabel: UILabel = {
        let lb = UILabel()
        lb.font = .systemFont(ofSize: 16, weight: .medium)
        lb.textColor = UIColor(hex: "#333333")
        lb.numberOfLines = 1
        return lb
    }()

    /// 商品副标题/信息
    private let subtitleLabel: UILabel = {
        let lb = UILabel()
        lb.font = .systemFont(ofSize: 11)
        lb.textColor = UIColor(hex: "#999999")
        return lb
    }()

    /// 会员价徽章
    private let memberBadge: UILabel = {
        let lb = UILabel()
        lb.text = "会员价"
        lb.font = .systemFont(ofSize: 12, weight: .medium)
        lb.textColor = .white
        lb.backgroundColor = UIColor(hex: "#FF5858")
        lb.textAlignment = .center
        lb.layer.cornerRadius = 4
        lb.clipsToBounds = true
        return lb
    }()

    /// 其他促销徽章
    private let extraBadge: UILabel = {
        let lb = UILabel()
        lb.text = "半年低价"
        lb.font = .systemFont(ofSize: 12, weight: .medium)
        lb.textColor = .white
        lb.backgroundColor = UIColor(hex: "#65C27B")
        lb.textAlignment = .center
        lb.layer.cornerRadius = 4
        lb.clipsToBounds = true
        return lb
    }()

    /// 会员价数字富文本
    private let priceLabel: UILabel = {
        let lb = UILabel()
        lb.font = .systemFont(ofSize: 20, weight: .bold)
        lb.textColor = UIColor(hex: "#F97B2A")
        return lb
    }()

    /// 原价删除线文本
    private let originalPriceLabel: UILabel = {
        let lb = UILabel()
        lb.font = .systemFont(ofSize: 11)
        lb.textColor = UIColor(hex: "#BBBBBB")
        return lb
    }()

    /// 进店抢购按钮
    private let actionButton: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setTitle("进店抢购", for: .normal)
        btn.setTitleColor(UIColor(hex: "#FF1515"), for: .normal)
        btn.titleLabel?.font = .systemFont(ofSize: 13, weight: .medium)
        btn.layer.cornerRadius = 8
        btn.layer.borderWidth = 1
        btn.layer.borderColor = UIColor(hex: "#FF1515").cgColor
        return btn
    }()

    // MARK: - Init
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        selectionStyle = .none
        backgroundColor = .clear
        setupUI()
    }
    required init?(coder: NSCoder) { fatalError("init(coder:) has not been implemented") }

    private func setupUI() {
        separator.backgroundColor = UIColor(hex: "#EAEAEA")
        contentView.addSubview(cardView)
        [shopIcon, shopNameLabel, categoryLabel, ratingLabel, starStack, avgPriceLabel, distanceLabel, separator, productImageView, productNameLabel, subtitleLabel, memberBadge, extraBadge, priceLabel, originalPriceLabel, actionButton].forEach { cardView.addSubview($0) }

        cardView.snp.makeConstraints { $0.edges.equalToSuperview().inset(UIEdgeInsets(top: 6, left: 8, bottom: 6, right: 8)) }

        // MARK: - 顶部店铺信息布局
        shopIcon.snp.makeConstraints { make in
            make.left.top.equalToSuperview().inset(10)
            make.size.equalTo(CGSize(width: 32, height: 32))
        }
        shopNameLabel.snp.makeConstraints { make in
            make.left.equalTo(shopIcon.snp.right).offset(6)
            make.top.equalTo(shopIcon)
        }
        categoryLabel.snp.makeConstraints { make in
            make.left.equalTo(shopNameLabel.snp.right).offset(6)
            make.centerY.equalTo(shopNameLabel)
        }
        distanceLabel.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-10)
            make.centerY.equalTo(shopIcon)
        }

        // MARK: - 评分 / 人均 行布局
        // 设置星星容器属性
        starStack.axis = .horizontal
        starStack.spacing = 1
        starStack.alignment = .center
        ratingLabel.setContentHuggingPriority(.required, for: .horizontal)
        ratingLabel.setContentCompressionResistancePriority(.required, for: .horizontal)
        ratingLabel.snp.makeConstraints { make in
            make.left.equalTo(shopNameLabel)
            make.top.equalTo(shopNameLabel.snp.bottom).offset(4)
        }
        starStack.snp.makeConstraints { make in
            make.left.equalTo(ratingLabel.snp.right).offset(4)
            make.centerY.equalTo(ratingLabel)
            make.height.equalTo(16)
        }
        avgPriceLabel.snp.remakeConstraints { make in
            make.left.equalTo(starStack.snp.right).offset(9)
            make.centerY.equalTo(ratingLabel)
        }

        separator.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(10)
            make.top.equalToSuperview().offset(56)
            make.height.equalTo(0.5)
        }

        // MARK: - 商品信息区域
        productImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(10)
            make.top.equalTo(separator.snp.bottom).offset(10)
            make.bottom.equalToSuperview().offset(-10)
            make.size.equalTo(CGSize(width: 96, height: 68))
        }
        productNameLabel.snp.makeConstraints { make in
            make.top.equalTo(productImageView)
            make.left.equalTo(productImageView.snp.right).offset(10)
            make.right.equalToSuperview().offset(-10)
        }
        subtitleLabel.snp.makeConstraints { make in
            make.left.equalTo(productNameLabel)
            make.top.equalTo(productNameLabel.snp.bottom).offset(4)
        }
        memberBadge.snp.makeConstraints { make in
            make.left.equalTo(productNameLabel)
            make.top.equalTo(subtitleLabel.snp.bottom).offset(6)
            make.height.equalTo(18)
            make.width.equalTo(44)
        }
        extraBadge.snp.makeConstraints { make in
            make.left.equalTo(memberBadge.snp.right).offset(6)
            make.centerY.equalTo(memberBadge)
            make.height.equalTo(18)
            make.width.equalTo(60)
        }
        priceLabel.snp.makeConstraints { make in
            make.left.equalTo(productNameLabel)
            make.bottom.equalTo(productImageView)
        }
        originalPriceLabel.snp.makeConstraints { make in
            make.left.equalTo(priceLabel.snp.right).offset(6)
            make.centerY.equalTo(priceLabel)
        }
        actionButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-10)
            make.bottom.equalTo(productImageView)
            make.width.equalTo(90)
            make.height.equalTo(32)
        }
    }

    private var didApplyInit = false
    // 重写 Cell 的 layoutSubviews 方法，确保获得正确的尺寸
    override func layoutSubviews() {
        super.layoutSubviews()
    }

    // 保存当前数据模型的引用
    private var currentShop: GroupBuyShopItem?

    // 重写准备复用方法，清理状态
    override func prepareForReuse() {
        super.prepareForReuse()
        currentShop = nil
        memberBadge.isHidden = true
        extraBadge.isHidden = true
        // 清理图片，避免复用时显示错误的图片
        shopIcon.image = nil
        productImageView.image = nil
    }

    func configure(with model: GroupBuyShopItem) {
        // 保存模型引用
        currentShop = model

        // 店铺信息
        shopNameLabel.text = model.shopName
        categoryLabel.text = model.categoryName
        let score = model.shopScore ?? 0
        ratingLabel.text = String(format: "%.1f分", score)
        updateStars(rating: score)
        if let avg = model.orderPriceAvg {
            avgPriceLabel.text = "人均￥" + avg
        } else {
            avgPriceLabel.text = "人均-"
        }
        if let dist = model.distance {
            distanceLabel.text = String(format: "%.2f千米", dist)
        } else {
            distanceLabel.text = "--"
        }

        // 商品信息
        if let good = model.goodResultVO {
            productNameLabel.text = good.goodName
            
            // 标签处理 - 使用固定宽度计算，不依赖布局
            if let labels = good.labelNameList, !labels.isEmpty {
                // 使用固定宽度计算，避免依赖布局
                let screenWidth = UIScreen.main.bounds.width
                let cardWidth = screenWidth - 16 // 左右各8点边距
                let leftPadding: CGFloat = 116 // 图片(96) + 左内边距(10) + 间距(10)
                let rightPadding: CGFloat = 110 // 按钮(90) + 右内边距(10) + 间距(10)
                let availableWidth = cardWidth - leftPadding - rightPadding
                
                // 第一个标签
                if labels.count > 0 {
                    let firstLabel = labels[0]
                    memberBadge.text = firstLabel
                    let firstWidth = max(44, textWidth(for: firstLabel, font: memberBadge.font) + 16)
                    
                    if firstWidth <= availableWidth {
                        memberBadge.isHidden = false
                        memberBadge.snp.remakeConstraints { make in
                            make.left.equalTo(productNameLabel)
                            make.top.equalTo(subtitleLabel.snp.bottom).offset(6)
                            make.height.equalTo(18)
                            make.width.equalTo(firstWidth)
                        }
                        
                        // 第二个标签
                        if labels.count > 1 {
                            let secondLabel = labels[1]
                            extraBadge.text = secondLabel
                            let secondWidth = max(44, textWidth(for: secondLabel, font: extraBadge.font) + 16)
                            
                            if firstWidth + 6 + secondWidth <= availableWidth {
                                extraBadge.isHidden = false
                                extraBadge.snp.remakeConstraints { make in
                                    make.left.equalTo(memberBadge.snp.right).offset(6)
                                    make.centerY.equalTo(memberBadge)
                                    make.height.equalTo(18)
                                    make.width.equalTo(secondWidth)
                                }
                            } else {
                                extraBadge.isHidden = true
                            }
                        } else {
                            extraBadge.isHidden = true
                        }
                    } else {
                        memberBadge.isHidden = true
                        extraBadge.isHidden = true
                    }
                } else {
                    memberBadge.isHidden = true
                    extraBadge.isHidden = true
                }
            } else {
                memberBadge.isHidden = true
                extraBadge.isHidden = true
            }

            subtitleLabel.text = good.labelNameList?.first ?? ""

            // 会员价
            let priceValue = good.activePrice ?? 0
            let priceString = String(format: "%.2f", priceValue)
            let parts = priceString.split(separator: ".", omittingEmptySubsequences: false)
            let integerP = String(parts.first ?? "")
            let decimalP = parts.count>1 ? "." + parts[1] : ""
            let unitAttr = [NSAttributedString.Key.font: UIFont.systemFont(ofSize: 11), NSAttributedString.Key.foregroundColor: UIColor(hex: "#F97B2A")]
            let bigAttr = [NSAttributedString.Key.font: UIFont.systemFont(ofSize: 20, weight: .bold), NSAttributedString.Key.foregroundColor: UIColor(hex: "#F97B2A")]
            let smallAttr = [NSAttributedString.Key.font: UIFont.systemFont(ofSize: 11), NSAttributedString.Key.foregroundColor: UIColor(hex: "#F97B2A")]
            let priceAttr = NSMutableAttributedString(string: "￥", attributes: unitAttr)
            priceAttr.append(NSAttributedString(string: integerP, attributes: bigAttr))
            priceAttr.append(NSAttributedString(string: decimalP, attributes: smallAttr))
            priceLabel.attributedText = priceAttr

            if let source = good.sourcePrice {
                let originAttr = NSAttributedString(string: "￥" + source, attributes: [
                    .strikethroughStyle: NSUnderlineStyle.single.rawValue,
                    .foregroundColor: UIColor(hex: "#BBBBBB"),
                    .font: UIFont.systemFont(ofSize: 11)
                ])
                originalPriceLabel.attributedText = originAttr
            }

            // 商品图片 - 处理多张图片用逗号分隔的情况，取第一张
            if let imgStr = good.goodImages, !imgStr.isEmpty {
                // 分割图片URL字符串，取第一张图片
                let imageUrls = imgStr.components(separatedBy: ",")
                if let firstImageUrl = imageUrls.first?.trimmingCharacters(in: .whitespacesAndNewlines),
                   !firstImageUrl.isEmpty,
                   let url = URL(string: firstImageUrl) {
                    print("[GroupBuy] 设置商品图片: \(firstImageUrl)")
                    productImageView.kf.setImage(with: url, placeholder: UIImage(named: "placeholder_image"))
                } else {
                    print("[GroupBuy] 商品图片URL无效: \(imgStr)")
                }
            } else {
                print("[GroupBuy] 商品图片为空")
            }
        }

        // 店铺头像
        if let shopImgStr = model.shopHeadImage, !shopImgStr.isEmpty, let url = URL(string: shopImgStr) {
            print("[GroupBuy] 设置店铺头像: \(shopImgStr)")
            shopIcon.kf.setImage(with: url, placeholder: UIImage(named: "default_shop_avatar"))
        } else {
            print("[GroupBuy] 店铺头像为空，shopHeadImage: \(model.shopHeadImage ?? "nil")")
            shopIcon.image = UIImage(named: "default_shop_avatar")
        }
    }

    private func updateStars(rating: Double) {
        starStack.arrangedSubviews.forEach { $0.removeFromSuperview() }
        let full = max(0, min(5, Int(floor(rating))))
        for _ in 0..<full {
            let iv = UIImageView(image: UIImage(named: "icon_star_fill"))
            iv.contentMode = .scaleAspectFit
            iv.snp.makeConstraints { $0.size.equalTo(CGSize(width: 16, height: 16)) }
            starStack.addArrangedSubview(iv)
        }
    }

    // MARK: - 标签处理
    private func updateLabels(with labels: [String]?) {
        // 调试信息
        print("[GroupBuyCell] updateLabels - cell width: \(bounds.width), actionButton: \(actionButton.frame)")
        
        // 首先隐藏所有标签
        memberBadge.isHidden = true
        extraBadge.isHidden = true
        
        guard let labels = labels, !labels.isEmpty else { return }
        guard bounds.width > 0 else {
            print("[GroupBuyCell] Cell 宽度为 0，跳过标签更新")
            return
        }
        guard actionButton.frame.width > 0 else {
            print("[GroupBuyCell] 按钮宽度为 0，跳过标签更新")
            return
        }
        
        // 计算可用区域 - 使用实际控件尺寸
        let rightLimit: CGFloat
        if actionButton.frame.minX > 0 {
            rightLimit = actionButton.frame.minX - 10
        } else {
            // 估算右侧边界 - 卡片宽度减去按钮宽度和右边距
            rightLimit = bounds.width - 90 - 20
        }
        
        let leftStart: CGFloat
        if productNameLabel.frame.minX > 0 {
            leftStart = productNameLabel.frame.minX
        } else {
            // 估算左侧边界 - 图片宽度加左边距和间距
            leftStart = 96 + 10 + 10
        }
        
        let availableWidth = rightLimit - leftStart
        print("[GroupBuyCell] 可用宽度: \(availableWidth), 左边界: \(leftStart), 右边界: \(rightLimit)")
        
        // 常量
        let badgeSpacing: CGFloat = 6  // 标签间距
        let minBadgeWidth: CGFloat = 44 // 最小标签宽度
        
        // 处理第一个标签
        if let firstLabel = labels.first {
            memberBadge.text = firstLabel
            let estimatedWidth = textWidth(for: firstLabel, font: memberBadge.font) + 16 // 文本宽度 + 内边距
            let badgeWidth = max(minBadgeWidth, estimatedWidth)
            
            print("[GroupBuyCell] 第一个标签: '\(firstLabel)', 宽度: \(badgeWidth)")
            // 判断是否有足够空间
            if badgeWidth <= availableWidth {
                memberBadge.isHidden = false
                memberBadge.snp.remakeConstraints { make in
                    make.left.equalTo(productNameLabel)
                    make.top.equalTo(subtitleLabel.snp.bottom).offset(6)
                    make.height.equalTo(18)
                    make.width.equalTo(badgeWidth)
                }
                
                // 检查是否有第二个标签
                if labels.count > 1 {
                    let secondLabel = labels[1]
                    extraBadge.text = secondLabel
                    let secondWidth = max(minBadgeWidth, textWidth(for: secondLabel, font: extraBadge.font) + 16)
                    
                    print("[GroupBuyCell] 第二个标签: '\(secondLabel)', 宽度: \(secondWidth), 剩余空间: \(availableWidth - badgeWidth - badgeSpacing)")
                    // 确保第二个标签也能放得下
                    if badgeWidth + badgeSpacing + secondWidth <= availableWidth {
                        extraBadge.isHidden = false
                        extraBadge.snp.remakeConstraints { make in
                            make.left.equalTo(memberBadge.snp.right).offset(badgeSpacing)
                            make.centerY.equalTo(memberBadge)
                            make.height.equalTo(18)
                            make.width.equalTo(secondWidth)
                        }
                        print("[GroupBuyCell] 第二个标签已显示")
                    } else {
                        print("[GroupBuyCell] 空间不足，第二个标签不显示")
                    }
                }
            } else {
                print("[GroupBuyCell] 空间不足，标签不显示")
            }
        }
    }
    
    // 计算文本宽度的辅助方法
    private func textWidth(for text: String, font: UIFont) -> CGFloat {
        let label = UILabel()
        label.text = text
        label.font = font
        label.sizeToFit()
        return label.frame.width
    }
}
