import UIKit
import SnapKit
import King<PERSON>er

// 引入模型
import SmartCodable

// 如果不同模块下可能需要手动 import 模型文件路径模块名（项目名默认为 Shuxia<PERSON>qi)。

class TakeoutTableViewCell: UITableViewCell {
    static let reuseIdentifier = "TakeoutTableViewCell"

    // MARK: - UI 组件
    private let cardView: UIView = {
        let v = UIView()
        v.backgroundColor = .white
        v.layer.cornerRadius = 16
        v.layer.shadowColor = UIColor.black.withAlphaComponent(0.05).cgColor
        v.layer.shadowOpacity = 1
        v.layer.shadowRadius = 4
        v.layer.shadowOffset = CGSize(width: 0, height: 2)
        return v
    }()

    // 店铺头部
    private let shopIconImageView: UIImageView = {
        let iv = UIImageView()
        iv.backgroundColor = UIColor(hex: "#EEEEEE")
        iv.layer.cornerRadius = 4
        iv.clipsToBounds = true
        iv.contentMode = .scaleAspectFill
        return iv
    }()

    private let shopNameLabel: UILabel = {
        let lb = UILabel()
        lb.font = .systemFont(ofSize: 15, weight: .medium)
        lb.textColor = UIColor(hex: "#333333")
        lb.numberOfLines = 1
        return lb
    }()

    private let categoryLabel: UILabel = {
        let lb = UILabel()
        lb.font = .systemFont(ofSize: 12)
        lb.textColor = UIColor(hex: "#777777")
        return lb
    }()

    private let ratingLabel: UILabel = {
        let lb = UILabel()
        lb.font = .systemFont(ofSize: 12)
        lb.textColor = UIColor(hex: "#FF8D36")
        return lb
    }()

    private let monthlySalesLabel: UILabel = {
        let lb = UILabel()
        lb.font = .systemFont(ofSize: 12)
        lb.textColor = UIColor(hex: "#777777")
        return lb
    }()

    private let averagePriceLabel: UILabel = {
        let lb = UILabel()
        lb.font = .systemFont(ofSize: 12)
        lb.textColor = UIColor(hex: "#777777")
        return lb
    }()

    private let distanceLabel: UILabel = {
        let lb = UILabel()
        lb.font = .systemFont(ofSize: 12)
        lb.textColor = UIColor(hex: "#777777")
        lb.textAlignment = .right
        return lb
    }()

    private let separator: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor(hex: "#EAEAEA")
        return v
    }()

    // 商品
    private let productImageView: UIImageView = {
        let iv = UIImageView()
        iv.contentMode = .scaleAspectFill
        iv.clipsToBounds = true
        iv.layer.cornerRadius = 6
        iv.backgroundColor = UIColor(hex: "#EEEEEE")
        return iv
    }()

    private let productNameLabel: UILabel = {
        let lb = UILabel()
        lb.font = .systemFont(ofSize: 16, weight: .medium)
        lb.textColor = UIColor(hex: "#333333")
        lb.numberOfLines = 1
        return lb
    }()

    // 配送信息行（11pt 灰色）
    private let deliveryInfoLabel: UILabel = {
        let lb = UILabel()
        lb.font = .systemFont(ofSize: 11)
        lb.textColor = UIColor(hex: "#999999")
        lb.numberOfLines = 1
        return lb
    }()

    private let memberPriceBadge: UILabel = {
        let lb = UILabel()
        lb.text = "会员价"
        lb.font = .systemFont(ofSize: 12, weight: .medium)
        lb.textColor = .white
        lb.backgroundColor = UIColor(hex: "#FF5858")
        lb.textAlignment = .center
        lb.layer.cornerRadius = 4
        lb.clipsToBounds = true
        return lb
    }()
    
    /// 其他促销徽章
    private let extraBadge: UILabel = {
        let lb = UILabel()
        lb.text = "半年低价"
        lb.font = .systemFont(ofSize: 12, weight: .medium)
        lb.textColor = .white
        lb.backgroundColor = UIColor(hex: "#65C27B")
        lb.textAlignment = .center
        lb.layer.cornerRadius = 4
        lb.clipsToBounds = true
        return lb
    }()

    private let priceLabel: UILabel = {
        let lb = UILabel()
        lb.font = .systemFont(ofSize: 20, weight: .bold)
        lb.textColor = UIColor(hex: "#F97B2A")
        return lb
    }()

    private let originalPriceLabel: UILabel = {
        let lb = UILabel()
        lb.font = .systemFont(ofSize: 11)
        lb.textColor = UIColor(hex: "#BBBBBB")
        return lb
    }()

    private let arrowImageView: UIImageView = {
        let iv = UIImageView(image: UIImage(named: "sharing_right_arrow"))
        iv.contentMode = .scaleAspectFit
        return iv
    }()

    // 评分 / 月售 / 人均  图标 + 文本组合
    private let ratingIcon = UIImageView(image: UIImage(named: "icon_rating_14"))
    private let ratingValueLabel: UILabel = {
        let lb = UILabel()
        lb.font = .systemFont(ofSize: 11)
        lb.textColor = UIColor(hex: "#FF1515")
        return lb
    }()

    private let salesIcon = UIImageView(image: UIImage(named: "icon_sales_14"))
    private let salesValueLabel: UILabel = {
        let lb = UILabel()
        lb.font = .systemFont(ofSize: 11)
        lb.textColor = UIColor(hex: "#FF8E15")
        return lb
    }()

    private let avgPriceIcon = UIImageView(image: UIImage(named: "icon_price_14"))
    private let avgPriceValueLabel: UILabel = {
        let lb = UILabel()
        lb.font = .systemFont(ofSize: 11)
        lb.textColor = UIColor(hex: "#65C27B")
        return lb
    }()

    private lazy var infoStack: UIStackView = {
        // 水平排列 rating, sales, avg price
        let ratingStack = UIStackView(arrangedSubviews: [ratingIcon, ratingValueLabel])
        ratingIcon.snp.makeConstraints { $0.size.equalTo(CGSize(width: 14, height: 14)) }
        let salesStack = UIStackView(arrangedSubviews: [salesIcon, salesValueLabel])
        salesIcon.snp.makeConstraints { $0.size.equalTo(CGSize(width: 14, height: 14)) }
        let avgStack = UIStackView(arrangedSubviews: [avgPriceIcon, avgPriceValueLabel])
        avgPriceIcon.snp.makeConstraints { $0.size.equalTo(CGSize(width: 14, height: 14)) }

        [ratingStack, salesStack, avgStack].forEach { stack in
            stack.axis = .horizontal
            stack.spacing = 4
            stack.alignment = .center
        }

        let main = UIStackView(arrangedSubviews: [ratingStack, salesStack, avgStack])
        main.axis = .horizontal
        main.spacing = 17
        main.alignment = .center
        return main
    }()
    
    // 保存当前数据模型的引用
    private var currentShop: TakeoutShopItem?

    // MARK: - Init
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        selectionStyle = .none
        backgroundColor = .clear
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // 重写准备复用方法，清理状态
    override func prepareForReuse() {
        super.prepareForReuse()
        currentShop = nil
        memberPriceBadge.isHidden = true
        extraBadge.isHidden = true
        // 清理图片，避免复用时显示错误的图片
        shopIconImageView.image = nil
        productImageView.image = nil
    }

    private func setupUI() {
        contentView.addSubview(cardView)
        // Header
        cardView.addSubview(shopIconImageView)
        cardView.addSubview(shopNameLabel)
        cardView.addSubview(categoryLabel)
        cardView.addSubview(ratingLabel)
        cardView.addSubview(monthlySalesLabel)
        cardView.addSubview(averagePriceLabel)
        cardView.addSubview(distanceLabel)
        cardView.addSubview(separator)
        cardView.addSubview(arrowImageView)
        cardView.addSubview(infoStack)
        // Product
        cardView.addSubview(productImageView)
        cardView.addSubview(productNameLabel)
        cardView.addSubview(deliveryInfoLabel)
        cardView.addSubview(memberPriceBadge)
        cardView.addSubview(extraBadge)
        cardView.addSubview(priceLabel)
        cardView.addSubview(originalPriceLabel)

        // Layout
        cardView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 6, left: 8, bottom: 6, right: 8))
        }

        shopIconImageView.snp.makeConstraints { make in
            make.left.top.equalToSuperview().inset(10)
            make.width.height.equalTo(32)
        }

        shopNameLabel.snp.makeConstraints { make in
            make.left.equalTo(shopIconImageView.snp.right).offset(6)
            make.top.equalTo(shopIconImageView)
        }

        categoryLabel.snp.makeConstraints { make in
            make.left.equalTo(shopNameLabel.snp.right).offset(6)
            make.centerY.equalTo(shopNameLabel)
        }

        arrowImageView.snp.makeConstraints { make in
            make.centerY.equalTo(shopIconImageView)
            make.right.equalToSuperview().offset(-10)
            make.width.height.equalTo(14)
        }

        distanceLabel.snp.makeConstraints { make in
            make.centerY.equalTo(shopIconImageView)
            make.right.equalTo(arrowImageView.snp.left).offset(-4)
        }

        infoStack.snp.makeConstraints { make in
            make.left.equalTo(shopNameLabel)
            make.top.equalTo(shopNameLabel.snp.bottom).offset(6)
        }

        separator.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(10)
            make.top.equalToSuperview().offset(56)
            make.height.equalTo(0.5)
        }

        productImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(10)
            make.top.equalTo(separator.snp.bottom).offset(10)
            make.bottom.equalToSuperview().offset(-10)
            make.width.equalTo(96)
            make.height.equalTo(68).priority(.high)
        }

        productNameLabel.snp.makeConstraints { make in
            make.top.equalTo(productImageView)
            make.left.equalTo(productImageView.snp.right).offset(10)
            make.right.equalToSuperview().offset(-10)
        }

        deliveryInfoLabel.snp.makeConstraints { make in
            make.left.equalTo(productNameLabel)
            make.top.equalTo(productNameLabel.snp.bottom).offset(4)
            make.right.equalTo(productNameLabel)
        }

        memberPriceBadge.snp.remakeConstraints { make in
            make.left.equalTo(productNameLabel)
            make.top.equalTo(deliveryInfoLabel.snp.bottom).offset(6)
            make.height.equalTo(18)
            make.width.equalTo(44)
        }
        
        extraBadge.snp.remakeConstraints { make in
            make.left.equalTo(memberPriceBadge.snp.right).offset(6)
            make.centerY.equalTo(memberPriceBadge)
            make.height.equalTo(18)
            make.width.equalTo(60)
        }

        priceLabel.snp.remakeConstraints { make in
            make.left.equalTo(productNameLabel)
            make.bottom.equalTo(productImageView)
        }

        originalPriceLabel.snp.remakeConstraints { make in
            make.left.equalTo(priceLabel.snp.right).offset(6)
            make.centerY.equalTo(priceLabel)
        }
    }

    // MARK: - Configure
    func configure(with item: TakeoutShopItem) {
        // 保存模型引用
        currentShop = item
        
        // 1. 顶部店铺信息
        shopNameLabel.text = item.shopName
        categoryLabel.text = item.categoryName ?? "-"

        // 评分
        let ratingStr: String
        if let score = item.shopScore, score > 0 {
            ratingStr = String(format: "%.1f", score)
        } else {
            ratingStr = "-"
        }
        ratingLabel.text = "评分" + ratingStr

        // 月售
        let saleStr = item.saleNum != nil ? String(item.saleNum!) : "-"
        monthlySalesLabel.text = "月售" + saleStr

        // 人均
        let avgPriceStr = item.orderPriceAvg ?? "-"
        averagePriceLabel.text = "人均￥" + avgPriceStr

        // 距离
        let distanceStr: String
        if item.distance >= 1 {
            distanceStr = String(format: "%.1fkm", item.distance)
        } else {
            distanceStr = String(format: "%.0fm", item.distance * 1000)
        }
        distanceLabel.text = distanceStr

        // 2. 商品信息（goodResultVO）
        if let good = item.goodResultVO {
            productNameLabel.text = good.goodName ?? ""

            // 标签处理 - 使用固定宽度计算，不依赖布局
            if let labels = good.labelNameList, !labels.isEmpty {
                // 使用固定宽度计算，避免依赖布局
                let screenWidth = UIScreen.main.bounds.width
                let cardWidth = screenWidth - 16 // 左右各8点边距
                let leftPadding: CGFloat = 116 // 图片(96) + 左内边距(10) + 间距(10)
                let rightPadding: CGFloat = 20 // 右内边距(10) + 间距(10)
                let availableWidth = cardWidth - leftPadding - rightPadding
                
                // 第一个标签
                if labels.count > 0 {
                    let firstLabel = labels[0]
                    memberPriceBadge.text = firstLabel
                    let firstWidth = max(44, textWidth(for: firstLabel, font: memberPriceBadge.font) + 16)
                    
                    if firstWidth <= availableWidth {
                        memberPriceBadge.isHidden = false
                        memberPriceBadge.snp.remakeConstraints { make in
                            make.left.equalTo(productNameLabel)
                            make.top.equalTo(deliveryInfoLabel.snp.bottom).offset(6)
                            make.height.equalTo(18)
                            make.width.equalTo(firstWidth)
                        }
                        
                        // 第二个标签
                        if labels.count > 1 {
                            let secondLabel = labels[1]
                            extraBadge.text = secondLabel
                            let secondWidth = max(44, textWidth(for: secondLabel, font: extraBadge.font) + 16)
                            
                            if firstWidth + 6 + secondWidth <= availableWidth {
                                extraBadge.isHidden = false
                                extraBadge.snp.remakeConstraints { make in
                                    make.left.equalTo(memberPriceBadge.snp.right).offset(6)
                                    make.centerY.equalTo(memberPriceBadge)
                                    make.height.equalTo(18)
                                    make.width.equalTo(secondWidth)
                                }
                            } else {
                                extraBadge.isHidden = true
                            }
                        } else {
                            extraBadge.isHidden = true
                        }
                    } else {
                        memberPriceBadge.isHidden = true
                        extraBadge.isHidden = true
                    }
                } else {
                    memberPriceBadge.isHidden = true
                    extraBadge.isHidden = true
                }
            } else {
                memberPriceBadge.isHidden = true
                extraBadge.isHidden = true
            }

            // 配送信息：起送/配送费/分钟
            let startFeeStr = item.startFee != nil ? String(format: "%.0f", item.startFee!) : "-"
            let deliveryFeeStr = item.deliveryFee != nil ? String(format: "%.0f", item.deliveryFee!) : "-"
            let minuteStr = item.minute != nil ? String(item.minute!) : "-"
            deliveryInfoLabel.text = "\(startFeeStr)起送  配送费\(deliveryFeeStr)  配送约\(minuteStr)分钟"

            // 会员价（使用 activePrice ）
            if let active = good.activePrice {
                setPriceLabel(amount: active)
            } else {
                priceLabel.text = "--"
            }

            // 原价（使用 sourcePrice ）
            if let origin = good.sourcePrice {
                setOriginalPriceLabel(price: origin)
            } else {
                originalPriceLabel.text = ""
            }

            // 商品图片 - 处理多张图片用逗号分隔的情况，取第一张
            if let img = good.goodImages, !img.isEmpty {
                // 分割图片URL字符串，取第一张图片
                let imageUrls = img.components(separatedBy: ",")
                if let firstImageUrl = imageUrls.first?.trimmingCharacters(in: .whitespacesAndNewlines),
                   !firstImageUrl.isEmpty,
                   let url = URL(string: firstImageUrl) {
                    print("[Takeout] 设置商品图片: \(firstImageUrl)")
                    productImageView.kf.setImage(with: url, placeholder: UIImage(named: "placeholder_image"))
                } else {
                    print("[Takeout] 商品图片URL无效: \(img)")
                }
            } else {
                print("[Takeout] 商品图片为空")
            }
        } else {
            // goodResultVO 为空的容错处理
            productNameLabel.text = ""
            deliveryInfoLabel.text = ""
            memberPriceBadge.isHidden = true
            extraBadge.isHidden = true
            priceLabel.text = "--"
            originalPriceLabel.text = ""
        }

        // 下方 infoStack 数值
        ratingValueLabel.text = ratingStr
        salesValueLabel.text = saleStr
        avgPriceValueLabel.text = avgPriceStr

        // 店铺头像 - 使用 shopHeadImage 而不是商品图片
        if let shopImgStr = item.shopHeadImage, !shopImgStr.isEmpty, let url = URL(string: shopImgStr) {
            print("[Takeout] 设置店铺头像: \(shopImgStr)")
            shopIconImageView.kf.setImage(with: url, placeholder: UIImage(named: "default_shop_avatar"))
        } else {
            print("[Takeout] 店铺头像为空，shopHeadImage: \(item.shopHeadImage ?? "nil")")
            shopIconImageView.image = UIImage(named: "default_shop_avatar")
        }
    }

    // MARK: - Helpers
    private func setPriceLabel(amount: Double) {
        let priceString = String(format: "%.2f", amount)
        let unitAttr: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 11),
            .foregroundColor: UIColor(hex: "#F97B2A")
        ]
        let bigAttr: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 20, weight: .bold),
            .foregroundColor: UIColor(hex: "#F97B2A")
        ]
        let smallAttr: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 11),
            .foregroundColor: UIColor(hex: "#F97B2A")
        ]
        let parts = priceString.split(separator: ".", omittingEmptySubsequences: false)
        let integerPart = String(parts.first ?? "")
        let decimalPart = parts.count>1 ? "." + parts[1] : ""
        let att = NSMutableAttributedString(string: "￥", attributes: unitAttr)
        att.append(NSAttributedString(string: integerPart, attributes: bigAttr))
        att.append(NSAttributedString(string: decimalPart, attributes: smallAttr))
        priceLabel.attributedText = att
    }

    private func setOriginalPriceLabel(price: String) {
        let attr = NSAttributedString(string: "￥" + price, attributes: [
            .strikethroughStyle: NSUnderlineStyle.single.rawValue,
            .foregroundColor: UIColor(hex: "#BBBBBB"),
            .font: UIFont.systemFont(ofSize: 11)
        ])
        originalPriceLabel.attributedText = attr
    }
    
    // 计算文本宽度的辅助方法
    private func textWidth(for text: String, font: UIFont) -> CGFloat {
        let label = UILabel()
        label.text = text
        label.font = font
        label.sizeToFit()
        return label.frame.width
    }
}
