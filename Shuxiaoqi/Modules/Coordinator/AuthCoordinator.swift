import UIKit
import ATAuthSDK

/// 普通登录页委托协议（由 LoginViewController2_0 触发）
protocol NormalLoginDelegate: AnyObject {
    /// 在普通登录页点击了一键登录按钮
    func normalLoginDidRequestOneClick(_ vc: UIViewController)
}

/// 登录流程协调器：负责在应用内统一管理“一键登录 / 普通登录”完整流程
final class AuthCoordinator: NSObject {
    static let shared = AuthCoordinator()
    private override init() {}

    // 保存外部回调，登录成功后告知调用者
    private var completion: ((Bool) -> Void)?
    // 当前呈现的登录相关 VC（普通登录页）
    private weak var currentLoginVC: UIViewController?
    // 调起登录的 Presenter，用于后续再次 present
    private weak var presenterVC: UIViewController?
    private var flowFinished = false

    /// 统一入口：开始登录流程
    /// - Parameters:
    ///   - presenter: 用来 present 登录 VC 的上层控制器
    ///   - preferOneClick: 是否优先尝试一键登录
    ///   - completion: 登录结果回调（true=成功）
    func startLogin(from presenter: UIViewController,
                    preferOneClick: Bool = true,
                    completion: @escaping (Bool) -> Void) {
        // 已经处于登录流程中时忽略新的请求
        guard currentLoginVC == nil else { return }
        self.presenterVC = presenter
        self.completion = completion
        self.flowFinished = false

        if preferOneClick {
            tryOneClickLogin(from: presenter)
        } else {
            presentNormalLogin(from: presenter)
        }
    }

    // MARK: - 私有流程封装
    private func tryOneClickLogin(from presenter: UIViewController) {
        LoginHelper.shared.presentOneClickLogin(from: presenter) { [weak self] success, message in
            guard let self = self, !self.flowFinished else { return }
            if success, let token = message {
                // 一键登录成功
                self.handleLoginSuccess(token: token)
            } else {
                // 失败或用户选择其他方式
                if message == "用户选择其他登录方式" {
                    self.presentNormalLogin(from: presenter)
                } else if message == "用户取消登录" {
                    // 用户主动取消，直接结束流程
                    self.finishFlow(success: false)
                } else {
                    // 其他错误（如环境不支持、没有SIM卡等）自动fallback到自定义登录页
                    print("一键登录失败，自动fallback到自定义登录页: \(message ?? "未知错误")")
                    self.presentNormalLogin(from: presenter)
                }
            }
        }
    }

    private func presentNormalLogin(from presenter: UIViewController) {
        guard !flowFinished else { return }
        let loginVC = LoginViewController2_0()
        loginVC.modalPresentationStyle = .fullScreen
        loginVC.delegate = self
        presenter.present(loginVC, animated: true)
        self.currentLoginVC = loginVC
    }

    private func handleLoginSuccess(token aliyunToken: String) {
        // 调用后台接口换取真正的登录 token
        APIManager.shared.loginWithOneClickToken(token: aliyunToken) { [weak self] result in
            guard let self = self, !self.flowFinished else { return }
            switch result {
            case .success(let resp):
                // 基本校验
                guard resp.status == 200, let data = resp.data else {
                    self.finishFlow(success: false)
                    return
                }
                switch data.state {
                case 0, 2:
                    let backendToken = data.tokenValue
                    if backendToken.isEmpty {
                        self.finishFlow(success: false)
                        return
                    }
                    AuthManager.shared.loginSuccess(token: backendToken)
                    // 登录成功后调用 firstEnter 记录设备信息
                    self.triggerFirstEnterAfterLogin()
                    // 关闭授权页
                    TXCommonHandler.sharedInstance().cancelLoginVC(animated: true) { [weak self] in
                        self?.finishFlow(success: true)
                    }
                case 1:
                    // 需要绑定手机或设置密码，转普通登录
                    DispatchQueue.main.async {
                        self.presentNormalLogin(from: self.presenterVC ?? UIViewController())
                    }
                default:
                    self.finishFlow(success: false)
                }
            case .failure(_):
                self.finishFlow(success: false)
            }
        }
    }

    private func finishFlow(success: Bool) {
        guard !flowFinished else { return }
        flowFinished = true
        // 关闭普通登录页（如果存在）
        currentLoginVC?.dismiss(animated: true)
        currentLoginVC = nil
        // 清理并回调
        completion?(success)
        completion = nil
    }

    /// 登录成功后调用 firstEnter 记录设备信息
    private func triggerFirstEnterAfterLogin() {
        print("[AuthCoordinator] 开始执行 triggerFirstEnterAfterLogin 方法")

        let deviceId = UIDevice.current.identifierForVendor?.uuidString ?? ""
        let deviceName = DeviceUtils.marketingDeviceModel()
        let deviceSystem = UIDevice.current.systemVersion
        let deviceType = 1

        print("[AuthCoordinator] 设备信息 - deviceId: \(deviceId), deviceName: \(deviceName), deviceSystem: \(deviceSystem)")

        // 获取本地IP
        let localIP = DeviceUtils.getCurrentIP()
        print("[AuthCoordinator] 本地IP: \(localIP)")

        // 设置超时机制，确保即使位置获取失败也会调用API
        var hasCalledAPI = false
        let timeoutSeconds: TimeInterval = 8.0

        // 超时回调
        DispatchQueue.main.asyncAfter(deadline: .now() + timeoutSeconds) {
            if !hasCalledAPI {
                print("[AuthCoordinator] 位置获取超时(\(timeoutSeconds)秒)，使用默认值调用API")
                hasCalledAPI = true
                self.callFirstEnterAPIFromCoordinator(
                    deviceId: deviceId,
                    deviceName: deviceName,
                    deviceSystem: deviceSystem,
                    deviceType: deviceType,
                    loginAddress: "",
                    ip: localIP
                )
            }
        }

        // 尝试获取位置信息
        print("[AuthCoordinator] 开始获取位置信息...")
        LocationManager.shared.getCurrentAreaCode { areaCode, address in
            print("[AuthCoordinator] 位置信息回调 - areaCode: \(areaCode ?? "nil"), address: \(address ?? "nil")")
            let loginAddress = address ?? ""

            // 获取公网IP
            print("[AuthCoordinator] 开始获取公网IP...")
            DeviceUtils.getPublicIP { publicIP in
                print("[AuthCoordinator] 公网IP回调 - publicIP: \(publicIP ?? "nil")")
                let finalIP = publicIP ?? localIP

                // 确保只调用一次API
                if !hasCalledAPI {
                    hasCalledAPI = true
                    self.callFirstEnterAPIFromCoordinator(
                        deviceId: deviceId,
                        deviceName: deviceName,
                        deviceSystem: deviceSystem,
                        deviceType: deviceType,
                        loginAddress: loginAddress,
                        ip: finalIP
                    )
                }
            }
        }
    }

    /// AuthCoordinator 中调用firstEnter API的辅助方法
    private func callFirstEnterAPIFromCoordinator(deviceId: String, deviceName: String, deviceSystem: String, deviceType: Int, loginAddress: String, ip: String) {
        // 更新并保存当前设备信息
        let currentDeviceInfo = DeviceUtils.CurrentDeviceInfo(
            deviceId: deviceId,
            deviceName: deviceName,
            deviceSystem: deviceSystem,
            deviceType: deviceType,
            ip: ip,
            location: loginAddress
        )
        DeviceUtils.saveCurrentDeviceInfo(currentDeviceInfo)
        print("[AuthCoordinator] 设备信息已保存")

        // 调用firstEnter接口
        print("[AuthCoordinator] 准备调用 firstEnter API - deviceId: \(deviceId), deviceName: \(deviceName), deviceSystem: \(deviceSystem), deviceType: \(deviceType), loginAddress: \(loginAddress)")

        APIManager.shared.firstEnter(
            deviceId: deviceId,
            deviceName: deviceName,
            deviceSystem: deviceSystem,
            deviceType: deviceType,
            loginAddress: loginAddress
        ) { result in
            print("[AuthCoordinator] API 调用完成，收到回调")
            switch result {
            case .success(let resp):
                print("[AuthCoordinator] firstEnter success: \(resp.displayMessage)")
            case .failure(let err):
                print("[AuthCoordinator] firstEnter failed: \(err)")
            }
        }
        print("[AuthCoordinator] firstEnter API 调用已发起")
    }
}

// MARK: - NormalLoginDelegate
extension AuthCoordinator: NormalLoginDelegate {
    func normalLoginDidRequestOneClick(_ vc: UIViewController) {
        // 先关闭普通登录页，再调起一键登录
        vc.dismiss(animated: false) { [weak self] in
            guard let self = self, let presenter = self.presenterVC else { return }
            self.currentLoginVC = nil
            self.tryOneClickLogin(from: presenter)
        }
    }
} 
